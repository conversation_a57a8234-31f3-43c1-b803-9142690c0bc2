class_name ArtifactInventoryComponent
extends Node

signal artifact_added(artifact_data: ArtifactData)
signal artifact_removed(artifact_data: ArtifactData)

var _artifacts: Array[ArtifactData] = []
var _ability_component: AbilityComponent

func _ready() -> void:
	_ability_component = get_parent().find_child(&"AbilityComponent", true, false)
	if not is_instance_valid(_ability_component):
		push_error("ArtifactInventoryComponent: Failed to find AbilityComponent.")

func add_artifact(artifact_data: ArtifactData) -> void:
	if not is_instance_valid(artifact_data):
		push_error("ArtifactInventoryComponent: Provided artifact_data is invalid.")
		return

	if has_artifact(artifact_data.id):
		push_warning("ArtifactInventoryComponent: Artifact with id '%s' already exists." % artifact_data.id)
		return

	_artifacts.append(artifact_data)

	if is_instance_valid(artifact_data.ability_data) and is_instance_valid(_ability_component):
		_ability_component._add_ability(artifact_data.ability_data)

	artifact_added.emit(artifact_data)

func remove_artifact(artifact_id: StringName) -> void:
	var artifact_to_remove: ArtifactData = null

	for artifact: ArtifactData in _artifacts:
		if artifact.id == artifact_id:
			artifact_to_remove = artifact
			break

	if not is_instance_valid(artifact_to_remove):
		push_warning("ArtifactInventoryComponent: Artifact with id '%s' not found." % artifact_id)
		return

	_artifacts.erase(artifact_to_remove)

	if is_instance_valid(artifact_to_remove.ability_data) and is_instance_valid(_ability_component):
		for child: Node in _ability_component.get_children():
			if child is Ability:
				var ability: Ability = child as Ability
				if ability.data == artifact_to_remove.ability_data:
					_ability_component.remove_ability(ability)
					break

	artifact_removed.emit(artifact_to_remove)

func has_artifact(artifact_id: StringName) -> bool:
	for artifact: ArtifactData in _artifacts:
		if artifact.id == artifact_id:
			return true
	return false

func get_artifact(artifact_id: StringName) -> ArtifactData:
	for artifact: ArtifactData in _artifacts:
		if artifact.id == artifact_id:
			return artifact
	return null

func get_all_artifacts() -> Array[ArtifactData]:
	return _artifacts.duplicate()

func clear_all_artifacts() -> void:
	var artifacts_to_remove: Array[ArtifactData] = _artifacts.duplicate()
	for artifact: ArtifactData in artifacts_to_remove:
		remove_artifact(artifact.id)
